<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="29d11922-752d-4d5d-b5a5-b16a4a22fad3" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/roshad/ecommerce/auth/UserServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ecommerce" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ecommerce" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ecommerce" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="build" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2xnzaNxyYYEW8P05ECyDenMXVFk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle.ecommerce-backend [:io.github.roshad.ecommerce.EcommerceApplication.main()].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ecommerce-backend [build].executor&quot;: &quot;Run&quot;,
    &quot;Gradle.ecommerce-backend [clean build].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.keymap&quot;,
    &quot;应用程序.EcommerceApplication.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="gradle build --refresh-dependencies" />
      <command value="gradle build" />
      <command value="gradle clean build" />
    </option>
  </component>
  <component name="RunManager" selected="应用程序.EcommerceApplication">
    <configuration name="EcommerceApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="io.github.roshad.ecommerce.EcommerceApplication" />
      <module name="ecommerce.main" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="io.github.roshad.ecommerce.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ecommerce-backend [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="--refresh-dependencies" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ecommerce-backend [clean build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.EcommerceApplication" />
        <item itemvalue="Gradle.ecommerce-backend [build]" />
        <item itemvalue="Gradle.ecommerce-backend [clean build]" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="29d11922-752d-4d5d-b5a5-b16a4a22fad3" name="更改" comment="" />
      <created>1748588691793</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748588691793</updated>
    </task>
    <servers />
  </component>
</project>