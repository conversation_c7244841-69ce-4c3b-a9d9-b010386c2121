package io.github.roshad.ecommerce.auth;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserServiceImpl(UserMapper userMapper, RoleMapper roleMapper, PasswordEncoder passwordEncoder) {
        this.userMapper = userMapper;
        this.roleMapper = roleMapper;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public Long getUserId(String username) {
        User user = userMapper.findByUsername(username);
        return user != null ? user.getId() : null;
    }

    @Override
    public User getUserByUsername(String username) {
        User user = userMapper.findByUsername(username);
        if (user != null) {
            // 加載用戶角色
            List<Role> roles = roleMapper.findRolesByUserId(user.getId());
            user.setRoles(roles);
        }
        return user;
    }

    @Override
    public User getUserById(Long id) {
        User user = userMapper.findById(id);
        if (user != null) {
            // 加載用戶角色
            List<Role> roles = roleMapper.findRolesByUserId(user.getId());
            user.setRoles(roles);
        }
        return user;
    }

    @Override
    public User registerUser(RegisterRequest registerRequest) throws Exception {
        // 驗證密碼是否匹配
        if (!registerRequest.isPasswordMatching()) {
            throw new Exception("密碼和確認密碼不匹配");
        }

        // 檢查用戶名是否已存在
        if (isUsernameExists(registerRequest.getUsername())) {
            throw new Exception("用戶名已存在");
        }

        // 檢查郵箱是否已存在
        if (isEmailExists(registerRequest.getEmail())) {
            throw new Exception("郵箱已被註冊");
        }

        // 創建新用戶
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPasswordHash(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());

        // 插入數據庫
        int result = userMapper.insertUser(user);
        if (result > 0) {
            // 為新用戶分配默認USER角色
            Role userRole = roleMapper.findByName("USER");
            if (userRole != null) {
                roleMapper.assignRoleToUser(user.getId(), userRole.getId());
                // 重新加載用戶角色
                List<Role> roles = roleMapper.findRolesByUserId(user.getId());
                user.setRoles(roles);
            }
            return user;
        } else {
            throw new Exception("用戶註冊失敗");
        }
    }

    @Override
    public boolean isUsernameExists(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    @Override
    public boolean isEmailExists(String email) {
        return userMapper.countByEmail(email) > 0;
    }

    @Override
    public boolean updateUserInfo(Long id, String email) {
        return userMapper.updateUserInfo(id, email) > 0;
    }

    @Override
    public boolean updateUserPassword(Long id, String oldPassword, String newPassword) {
        User user = userMapper.findById(id);
        if (user == null) {
            return false;
        }

        // 驗證舊密碼
        if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
            return false;
        }

        // 更新密碼
        String encodedNewPassword = passwordEncoder.encode(newPassword);
        return userMapper.updateUserPassword(id, encodedNewPassword) > 0;
    }
}