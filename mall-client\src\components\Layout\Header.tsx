'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { MagnifyingGlassIcon, Bars3Icon, ShoppingCartIcon, UserIcon } from '@heroicons/react/24/outline';
import { useCartStore } from '../../store/cartStore';
import { useAuthStore } from '../../store/authStore';

const Header: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const router = useRouter();
  const { totalItems } = useCartStore();
  const { user, isAuthenticated, logout } = useAuthStore();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <header
      className="bg-gray-800 text-white p-4 shadow-md"
      style={{
        backgroundColor: '#1f2937',
        color: 'white',
        padding: '1rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        minHeight: '60px'
      }}
    >
      <div className="container mx-auto">
        <div className="flex justify-between items-center" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold">
            E-commerce
          </Link>

          {/* 搜索框 - 桌面版 */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </form>
          </div>

          {/* 導航菜單 - 桌面版 */}
          <nav className="hidden md:flex items-center space-x-4">
            <Link href="/" className="hover:text-gray-300">首頁</Link>
            <Link href="/products" className="hover:text-gray-300">商品</Link>
            <Link href="/orders" className="hover:text-gray-300">訂單</Link>

            {/* 購物車圖標 */}
            <Link href="/cart" className="relative hover:text-gray-300 flex items-center">
              <ShoppingCartIcon className="w-6 h-6" />
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {totalItems > 99 ? '99+' : totalItems}
                </span>
              )}
            </Link>

            {/* 用戶狀態 */}
            {isAuthenticated ? (
              <div className="relative group">
                <button className="flex items-center space-x-1 hover:text-gray-300">
                  <UserIcon className="w-5 h-5" />
                  <span>{user?.username}</span>
                </button>

                {/* 下拉菜單 */}
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    個人中心
                  </Link>
                  <Link href="/cart" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    購物車
                  </Link>
                  <button
                    onClick={() => {
                      logout();
                      router.push('/');
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    登出
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/login" className="hover:text-gray-300">登錄</Link>
                <Link href="/register" className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700">
                  註冊
                </Link>
              </div>
            )}
          </nav>

          {/* 移動端菜單按鈕 */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-white focus:outline-none"
            >
              <Bars3Icon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* 移動端菜單 */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            {/* 移動端搜索框 */}
            <form onSubmit={handleSearch} className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </form>

            {/* 移動端導航 */}
            <nav className="flex flex-col space-y-2">
              <Link href="/" className="block py-2 hover:text-gray-300">首頁</Link>
              <Link href="/products" className="block py-2 hover:text-gray-300">商品</Link>
              <Link href="/orders" className="block py-2 hover:text-gray-300">訂單</Link>

              {/* 移動端購物車鏈接 */}
              <Link href="/cart" className="flex items-center justify-between py-2 hover:text-gray-300">
                <span>購物車</span>
                {totalItems > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {totalItems > 99 ? '99+' : totalItems}
                  </span>
                )}
              </Link>

              {/* 移動端用戶狀態 */}
              {isAuthenticated ? (
                <>
                  <div className="border-t border-gray-600 pt-2 mt-2">
                    <div className="flex items-center space-x-2 py-2">
                      <UserIcon className="w-5 h-5" />
                      <span>歡迎，{user?.username}</span>
                    </div>
                  </div>
                  <Link href="/profile" className="block py-2 hover:text-gray-300">個人中心</Link>
                  <button
                    onClick={() => {
                      logout();
                      router.push('/');
                      setIsMobileMenuOpen(false);
                    }}
                    className="block w-full text-left py-2 hover:text-gray-300"
                  >
                    登出
                  </button>
                </>
              ) : (
                <>
                  <div className="border-t border-gray-600 pt-2 mt-2">
                    <Link href="/login" className="block py-2 hover:text-gray-300">登錄</Link>
                    <Link href="/register" className="block py-2 hover:text-gray-300">註冊</Link>
                  </div>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;