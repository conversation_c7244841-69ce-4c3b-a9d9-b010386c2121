// 訂單相關類型定義

// 訂單狀態枚舉
export enum OrderStatus {
  PENDING = 'PENDING',           // 待付款
  PAID = 'PAID',                 // 已付款
  PROCESSING = 'PROCESSING',     // 處理中
  SHIPPED = 'SHIPPED',           // 已發貨
  DELIVERED = 'DELIVERED',       // 已送達
  CANCELLED = 'CANCELLED',       // 已取消
  REFUNDED = 'REFUNDED'          // 已退款
}

// 支付方式枚舉
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',   // 信用卡
  DEBIT_CARD = 'DEBIT_CARD',     // 借記卡
  PAYPAL = 'PAYPAL',             // PayPal
  ALIPAY = 'ALIPAY',             // 支付寶
  WECHAT_PAY = 'WECHAT_PAY',     // 微信支付
  CASH_ON_DELIVERY = 'CASH_ON_DELIVERY' // 貨到付款
}

// 收貨地址接口
export interface Address {
  id?: number;
  userId?: number;
  recipientName: string;         // 收件人姓名
  phone: string;                 // 聯繫電話
  province: string;              // 省份
  city: string;                  // 城市
  district: string;              // 區/縣
  detailAddress: string;         // 詳細地址
  postalCode?: string;           // 郵政編碼
  isDefault: boolean;            // 是否為默認地址
  createdAt?: string;
  updatedAt?: string;
}

// 訂單商品項接口
export interface OrderItem {
  id?: number;
  orderId?: number;
  productId: number;
  productName: string;
  productImage: string;
  price: number;                 // 下單時的價格
  quantity: number;
  subtotal: number;              // 小計 (price * quantity)
}

// 訂單接口
export interface Order {
  id?: number;
  userId?: number;
  orderNumber: string;           // 訂單號
  status: OrderStatus;
  items: OrderItem[];
  
  // 價格信息
  subtotal: number;              // 商品小計
  shippingFee: number;           // 運費
  discount: number;              // 折扣金額
  totalAmount: number;           // 總金額
  
  // 收貨信息
  shippingAddress: Address;
  
  // 支付信息
  paymentMethod: PaymentMethod;
  paymentStatus: 'PENDING' | 'COMPLETED' | 'FAILED';
  paidAt?: string;
  
  // 時間戳
  createdAt: string;
  updatedAt: string;
  
  // 備註
  notes?: string;
}

// 創建訂單請求接口
export interface CreateOrderRequest {
  items: {
    productId: number;
    quantity: number;
  }[];
  shippingAddressId: number;
  paymentMethod: PaymentMethod;
  notes?: string;
}

// 訂單統計接口
export interface OrderSummary {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalSpent: number;
}
